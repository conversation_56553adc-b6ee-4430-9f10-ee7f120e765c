package source

import (
	"os"
	"strings"
)

type envSource struct {
	prefix string
	toDot  keyTransformer
}

func Env(prefix string, tf keyTransformer) *envSource {
	return &envSource{
		prefix: strings.ToUpper(strings.TrimSuffix(prefix, "_")),
		toDot:  tf,
	}
}

func (e *envSource) Name() string {
	return "env:" + e.prefix
}

func (e *envSource) Load() (map[string]any, error) {
	out := make(map[string]any)
	prefix := e.prefix
	for _, kv := range os.Environ() {
		if idx := strings.IndexByte(kv, '='); idx > 0 {
			k, v := kv[:idx], kv[idx+1:]
			uk := strings.ToUpper(k)
			if prefix != "" {
				p := prefix + "_"
				if !strings.HasPrefix(uk, p) {
					continue
				}
				k = k[len(p):]
			}
			k = strings.ToLower(k)
			k = strings.ReplaceAll(strings.ReplaceAll(k, "__", "."), "_", ".")
			k = e.toDot(k)
			setByPath(out, k, v)
		}
	}
	return out, nil
}

func (e *envSource) Watcher() (*noopWatcher, bool) {
	return &noopWatcher{}, false
}

// ============================ 工具函数 ============================

type keyTransformer func(k string) string

func setByPath(root map[string]any, path string, val any) {
	segs := strings.Split(path, ".")
	m := root
	for _, s := range segs[:len(segs)-1] {
		if _, ok := m[s]; !ok {
			m[s] = make(map[string]any)
		}
		m = m[s].(map[string]any)
	}
	m[segs[len(segs)-1]] = val
}

type noopWatcher struct{}

func (n *noopWatcher) Start(func()) error { return nil }
func (n *noopWatcher) Close() error       { return nil }
