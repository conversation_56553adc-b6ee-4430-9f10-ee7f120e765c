package source

import (
	"flag"
	"strings"
)

type flagSource struct {
	fs    *flag.FlagSet
	toDot keyTransformer
}

func Flag(fs *flag.FlagSet, tf keyTransformer) *flagSource {
	return &flagSource{fs: fs, toDot: tf}
}

func (f *flagSource) Name() string {
	return "flagset"
}

func (f *flagSource) Load() (map[string]any, error) {
	out := make(map[string]any)
	f.fs.Visit(func(fl *flag.Flag) {
		key := f.toDot(strings.ReplaceAll(strings.TrimSpace(fl.Name), "_", "."))
		setByPath(out, key, fl.Value.String())
	})
	return out, nil
}

func (f *flagSource) Watcher() (*noopWatcher, bool) {
	return &noopWatcher{}, false
}
