package source

import (
	"context"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

const (
	// 防抖间隔，避免重复事件
	debounceInterval = 5 * time.Millisecond
)

// File 实现基于文件的配置源
type File struct {
	mu         sync.RWMutex
	path       string
	cancel     context.CancelFunc
	watcher    *fsnotify.Watcher
	isWatching bool
}

// Provider 创建新的文件配置源
func Provider(path string) *File {
	return &File{
		path: filepath.Clean(path),
	}
}

// ReadBytes 读取文件内容
func (f *File) ReadBytes() ([]byte, error) {
	return os.ReadFile(f.path)
}

// Read 返回错误，文件提供者不支持此方法
func (f *File) Read() (map[string]any, error) {
	return nil, errors.New("file provider does not support this method")
}

// Watch 监听文件变化
func (f *File) Watch(callback func(event any, err error)) error {
	if err := f.initWatcher(); err != nil {
		return err
	}
	ctx, cancel := context.WithCancel(context.Background())
	f.mu.Lock()
	f.cancel = cancel
	f.mu.Unlock()
	go f.watchLoop(ctx, callback)
	return nil
}

// initWatcher 初始化文件监听器
func (f *File) initWatcher() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	if f.isWatching {
		return errors.New("file is already being watched")
	}

	// 验证文件路径是否有效
	if _, err := f.resolveSymlink(); err != nil {
		return fmt.Errorf("failed to resolve symlink: %w", err)
	}

	// 创建监听器
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("failed to create watcher: %w", err)
	}

	// 监听文件所在目录
	dir := filepath.Dir(f.path)
	if err := watcher.Add(dir); err != nil {
		watcher.Close()
		return fmt.Errorf("failed to add directory to watcher: %w", err)
	}

	f.watcher = watcher
	f.isWatching = true
	return nil
}

// resolveSymlink 解析符号链接并清理路径
func (f *File) resolveSymlink() (string, error) {
	realPath, err := filepath.EvalSymlinks(f.path)
	if err != nil {
		return "", err
	}
	return filepath.Clean(realPath), nil
}

// watchLoop 监听循环
func (f *File) watchLoop(ctx context.Context, callback func(event any, err error)) {
	defer f.cleanup()

	realPath, err := f.resolveSymlink()
	if err != nil {
		callback(nil, fmt.Errorf("failed to resolve initial symlink: %w", err))
		return
	}
	debouncer := newDebouncer(debounceInterval)
	for {
		select {
		case <-ctx.Done():
			return

		case event, ok := <-f.watcher.Events:
			if !ok {
				if f.isStillWatching() {
					callback(nil, errors.New("fsnotify watch channel closed"))
				}
				return
			}

			if debouncer.shouldSkip(event.String()) {
				continue
			}

			if err := f.handleFileEvent(event, &realPath, callback); err != nil {
				callback(nil, err)
				return
			}

		case err, ok := <-f.watcher.Errors:
			if !ok {
				if f.isStillWatching() {
					callback(nil, errors.New("fsnotify error channel closed"))
				}
				return
			}
			callback(nil, fmt.Errorf("watcher error: %w", err))
			return
		}
	}
}

// handleFileEvent 处理文件事件
func (f *File) handleFileEvent(event fsnotify.Event, realPath *string, callback func(event any, err error)) error {
	eventFile := filepath.Clean(event.Name)

	// 检查当前符号链接状态
	currentPath, err := f.resolveSymlink()
	if err != nil {
		return fmt.Errorf("failed to resolve symlink during event: %w", err)
	}

	isTargetFile := eventFile == *realPath || eventFile == f.path
	isSymlinkChanged := currentPath != "" && currentPath != *realPath

	// 处理创建和写入事件
	if event.Has(fsnotify.Create|fsnotify.Write) && (isTargetFile || isSymlinkChanged) {
		*realPath = currentPath
		callback(nil, nil)
		return nil
	}

	// 处理删除事件
	if isTargetFile && event.Has(fsnotify.Remove) {
		return fmt.Errorf("file %s was removed", event.Name)
	}

	return nil
}

// isStillWatching 检查是否仍在监听
func (f *File) isStillWatching() bool {
	f.mu.RLock()
	defer f.mu.RUnlock()
	return f.isWatching
}

// cleanup 清理资源
func (f *File) cleanup() {
	f.mu.Lock()
	defer f.mu.Unlock()

	f.isWatching = false
	if f.watcher != nil {
		f.watcher.Close()
		f.watcher = nil
	}
}

// debouncer 防抖器，避免重复事件
type debouncer struct {
	lastEvent     string
	lastEventTime time.Time
	interval      time.Duration
}

// newDebouncer 创建新的防抖器
func newDebouncer(interval time.Duration) *debouncer {
	return &debouncer{
		interval: interval,
	}
}

// shouldSkip 检查是否应该跳过此事件
func (d *debouncer) shouldSkip(event string) bool {
	now := time.Now()
	if event == d.lastEvent && now.Sub(d.lastEventTime) < d.interval {
		return true
	}
	d.lastEvent = event
	d.lastEventTime = now
	return false
}

// Unwatch 停止监听文件并关闭 fsnotify 监听器
func (f *File) Unwatch() error {
	f.mu.Lock()
	defer f.mu.Unlock()

	if !f.isWatching {
		return nil
	}

	// 取消监听循环
	if f.cancel != nil {
		f.cancel()
		f.cancel = nil
	}

	// 关闭监听器
	if f.watcher != nil {
		if err := f.watcher.Close(); err != nil {
			return fmt.Errorf("failed to close watcher: %w", err)
		}
		f.watcher = nil
	}

	f.isWatching = false
	return nil
}
