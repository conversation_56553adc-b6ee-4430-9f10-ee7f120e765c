package source

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
)

type File struct {
	mu         sync.Mutex
	path       string
	watcher    *fsnotify.Watcher
	isWatching bool
}

func Provider(path string) *File {
	return &File{path: filepath.Clean(path)}
}

func (f *File) ReadBytes() ([]byte, error) {
	return os.ReadFile(f.path)
}

func (f *File) Read() (map[string]any, error) {
	return nil, errors.New("file provider does not support this method")
}

func (f *File) Watch(callback func(event any, err error)) error {
	f.mu.Lock()
	if f.isWatching {
		f.mu.Unlock()
		return errors.New("file is already being watched")
	}
	realPath, err := filepath.EvalSymlinks(f.path)
	if err != nil {
		return err
	}
	realPath = filepath.Clean(realPath)
	fDir, _ := filepath.Split(f.path)
	f.watcher, err = fsnotify.NewWatcher()
	if err != nil {
		f.mu.Unlock()
		return err
	}

	f.isWatching = true
	err = f.watcher.Add(fDir)
	if err != nil {
		f.watcher.Close()
		f.watcher = nil
		f.isWatching = false
		f.mu.Unlock()
		return err
	}
	f.mu.Unlock()

	var (
		lastEvent     string
		lastEventTime time.Time
	)
	go func() {
	loop:
		for {
			select {
			case event, ok := <-f.watcher.Events:
				if !ok {
					f.mu.Lock()
					stillWatching := f.isWatching
					f.mu.Unlock()
					if stillWatching {
						callback(nil, errors.New("fsnotify watch channel closed"))
					}

					break loop
				}
				if event.String() == lastEvent && time.Since(lastEventTime) < time.Millisecond*5 {
					continue
				}
				lastEvent = event.String()
				lastEventTime = time.Now()
				evFile := filepath.Clean(event.Name)
				curPath, err := filepath.EvalSymlinks(f.path)
				if err != nil {
					callback(nil, err)
					break loop
				}
				curPath = filepath.Clean(curPath)
				onWatchedFile := evFile == realPath || evFile == f.path
				if event.Has(fsnotify.Create|fsnotify.Write) && (onWatchedFile ||
					(curPath != "" && curPath != realPath)) {
					realPath = curPath
					callback(nil, nil)
				} else if onWatchedFile && event.Has(fsnotify.Remove) {
					callback(nil, fmt.Errorf("file %s was removed", event.Name))
					break loop
				}
			case err, ok := <-f.watcher.Errors:
				if !ok {
					f.mu.Lock()
					stillWatching := f.isWatching
					f.mu.Unlock()
					if stillWatching {
						callback(nil, errors.New("fsnotify err channel closed"))
					}
					break loop
				}
				cb(nil, err)
				break loop
			}
		}

		f.mu.Lock()
		f.isWatching = false
		if f.watcher != nil {
			f.watcher.Close()
			f.watcher = nil
		}
		f.mu.Unlock()
	}()

	return nil
}

// Unwatch stops watching the files and closes fsnotify watcher.
func (f *File) Unwatch() error {
	f.mu.Lock()
	defer f.mu.Unlock()
	if !f.isWatching {
		return nil
	}
	f.isWatching = false
	if f.watcher != nil {
		return f.watcher.Close()
	}
	return errors.New("file watcher is in an inconsistent state: isWatching is true but watcher is nil")
}
