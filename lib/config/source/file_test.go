package source

import (
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"
)

func TestFileProvider(t *testing.T) {
	// 创建临时文件
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "test.txt")
	
	content := []byte("test content")
	if err := os.WriteFile(testFile, content, 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 测试 Provider 创建
	provider := Provider(testFile)
	if provider == nil {
		t.Fatal("Provider should not be nil")
	}

	// 测试 ReadBytes
	data, err := provider.ReadBytes()
	if err != nil {
		t.Fatalf("ReadBytes failed: %v", err)
	}
	if string(data) != "test content" {
		t.<PERSON>rrorf("Expected 'test content', got '%s'", string(data))
	}

	// 测试 Read 方法（应该返回错误）
	_, err = provider.Read()
	if err == nil {
		t.Error("Read should return an error")
	}
}

func TestFileWatch(t *testing.T) {
	// 创建临时文件
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "watch_test.txt")
	
	content := []byte("initial content")
	if err := os.WriteFile(testFile, content, 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	provider := Provider(testFile)
	
	var wg sync.WaitGroup
	var callbackCalled bool
	var mu sync.Mutex

	// 设置回调函数
	callback := func(event any, err error) {
		mu.Lock()
		defer mu.Unlock()
		if err != nil {
			t.Errorf("Callback received error: %v", err)
		}
		callbackCalled = true
		wg.Done()
	}

	// 开始监听
	if err := provider.Watch(callback); err != nil {
		t.Fatalf("Watch failed: %v", err)
	}

	// 等待一小段时间确保监听器启动
	time.Sleep(100 * time.Millisecond)

	wg.Add(1)
	
	// 修改文件
	newContent := []byte("modified content")
	if err := os.WriteFile(testFile, newContent, 0644); err != nil {
		t.Fatalf("Failed to modify test file: %v", err)
	}

	// 等待回调被调用
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 成功
	case <-time.After(2 * time.Second):
		t.Error("Callback was not called within timeout")
	}

	mu.Lock()
	if !callbackCalled {
		t.Error("Callback should have been called")
	}
	mu.Unlock()

	// 停止监听
	if err := provider.Unwatch(); err != nil {
		t.Errorf("Unwatch failed: %v", err)
	}
}

func TestFileWatchAlreadyWatching(t *testing.T) {
	tmpDir := t.TempDir()
	testFile := filepath.Join(tmpDir, "already_watching.txt")
	
	if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	provider := Provider(testFile)
	
	callback := func(event any, err error) {}

	// 第一次监听应该成功
	if err := provider.Watch(callback); err != nil {
		t.Fatalf("First Watch failed: %v", err)
	}

	// 第二次监听应该失败
	if err := provider.Watch(callback); err == nil {
		t.Error("Second Watch should have failed")
	}

	// 清理
	if err := provider.Unwatch(); err != nil {
		t.Errorf("Unwatch failed: %v", err)
	}
}

func TestDebouncer(t *testing.T) {
	debouncer := newDebouncer(10 * time.Millisecond)
	
	// 第一个事件不应该被跳过
	if debouncer.shouldSkip("event1") {
		t.Error("First event should not be skipped")
	}
	
	// 相同事件在间隔内应该被跳过
	if !debouncer.shouldSkip("event1") {
		t.Error("Duplicate event within interval should be skipped")
	}
	
	// 等待间隔过去
	time.Sleep(15 * time.Millisecond)
	
	// 现在相同事件不应该被跳过
	if debouncer.shouldSkip("event1") {
		t.Error("Event after interval should not be skipped")
	}
	
	// 不同事件不应该被跳过
	if debouncer.shouldSkip("event2") {
		t.Error("Different event should not be skipped")
	}
}
